name: Python Checks

on:
  merge_group:
  pull_request:
    branches:
      - main
      - 'release/**'

jobs:
  mypy-check:
    # See https://runs-on.com/runners/linux/
    runs-on: [runs-on,runner=8cpu-linux-x64,"run-id=${{ github.run_id }}"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: |
          backend/requirements/default.txt
          backend/requirements/dev.txt
          backend/requirements/model_server.txt
    - run: |
        python -m pip install --upgrade pip
        pip install --retries 5 --timeout 30 -r backend/requirements/default.txt
        pip install --retries 5 --timeout 30 -r backend/requirements/dev.txt
        pip install --retries 5 --timeout 30 -r backend/requirements/model_server.txt

    - name: Run MyPy
      run: |
        cd backend
        mypy .

    - name: Run ruff
      run: |
        cd backend
        ruff .

    - name: Check import order with reorder-python-imports
      run: |
        cd backend
        find ./danswer -name "*.py" | xargs reorder-python-imports --py311-plus

    - name: Check code formatting with Black
      run: |
        cd backend
        black --check .
