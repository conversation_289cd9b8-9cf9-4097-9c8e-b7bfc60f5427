name: Python Unit Tests

on:
  merge_group:
  pull_request:
    branches:
      - main
      - 'release/**'

jobs:
  backend-check:
    # See https://runs-on.com/runners/linux/
    runs-on: [runs-on,runner=8cpu-linux-x64,"run-id=${{ github.run_id }}"]

    env:
      PYTHONPATH: ./backend
      REDIS_CLOUD_PYTEST_PASSWORD: ${{ secrets.REDIS_CLOUD_PYTEST_PASSWORD }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        cache: 'pip'
        cache-dependency-path: |
          backend/requirements/default.txt
          backend/requirements/dev.txt

    - name: Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install --retries 5 --timeout 30 -r backend/requirements/default.txt
        pip install --retries 5 --timeout 30 -r backend/requirements/dev.txt

    - name: Run Tests
      shell: script -q -e -c "bash --noprofile --norc -eo pipefail {0}"
      run: py.test -o junit_family=xunit2 -xv --ff backend/tests/unit
