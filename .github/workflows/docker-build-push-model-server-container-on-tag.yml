name: Build and Push Model Server Image on Tag

on:
  push:
    tags:
      - "*"

env:
  REGISTRY_IMAGE: ${{ contains(github.ref_name, 'cloud') && 'onyxdotapp/onyx-model-server-cloud' || 'onyxdotapp/onyx-model-server' }}
  LATEST_TAG: ${{ contains(github.ref_name, 'latest') }}
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain

jobs:
  # 1) Preliminary job to check if the changed files are relevant
  check_model_server_changes:
    runs-on: ubuntu-latest
    outputs:
      changed: ${{ steps.check.outputs.changed }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check if relevant files changed
        id: check
        run: |
          # Default to "false"
          echo "changed=false" >> $GITHUB_OUTPUT

          # Compare the previous commit (github.event.before) to the current one (github.sha)
          # If any file in backend/model_server/** or backend/Dockerfile.model_server is changed,
          # set changed=true
          if git diff --name-only ${{ github.event.before }} ${{ github.sha }} \
             | grep -E '^backend/model_server/|^backend/Dockerfile.model_server'; then
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

  build-amd64:
    needs: [check_model_server_changes]
    if: needs.check_model_server_changes.outputs.changed == 'true'
    runs-on:
      [runs-on, runner=8cpu-linux-x64, "run-id=${{ github.run_id }}-amd64"]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: System Info
        run: |
          df -h
          free -h
          docker system prune -af --volumes

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:latest
            network=host

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and Push AMD64
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile.model_server
          platforms: linux/amd64
          push: true
          tags: ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-amd64
          build-args: |
            DANSWER_VERSION=${{ github.ref_name }}
          outputs: type=registry
          provenance: false

  build-arm64:
    needs: [check_model_server_changes]
    if: needs.check_model_server_changes.outputs.changed == 'true'
    runs-on:
      [runs-on, runner=8cpu-linux-x64, "run-id=${{ github.run_id }}-arm64"]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: System Info
        run: |
          df -h
          free -h
          docker system prune -af --volumes

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            image=moby/buildkit:latest
            network=host

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and Push ARM64
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile.model_server
          platforms: linux/arm64
          push: true
          tags: ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-arm64
          build-args: |
            DANSWER_VERSION=${{ github.ref_name }}
          outputs: type=registry
          provenance: false

  merge-and-scan:
    needs: [build-amd64, build-arm64, check_model_server_changes]
    if: needs.check_model_server_changes.outputs.changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Create and Push Multi-arch Manifest
        run: |
          docker buildx create --use
          docker buildx imagetools create -t ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }} \
            ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-amd64 \
            ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-arm64
          if [[ "${{ env.LATEST_TAG }}" == "true" ]]; then
            docker buildx imagetools create -t ${{ env.REGISTRY_IMAGE }}:latest \
              ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-amd64 \
              ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}-arm64
          fi

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        env:
          TRIVY_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-db:2"
          TRIVY_JAVA_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-java-db:1"
        with:
          image-ref: docker.io/${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}
          severity: "CRITICAL,HIGH"
          timeout: "10m"
