name: <PERSON><PERSON> - <PERSON><PERSON> and Test Charts

on:
  merge_group:
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # Allows manual triggering
  
jobs:
  helm-chart-check:
    # See https://runs-on.com/runners/linux/
    runs-on: [runs-on,runner=8cpu-linux-x64,hdd=256,"run-id=${{ github.run_id }}"]

    # fetch-depth 0 is required for helm/chart-testing-action
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: Set up He<PERSON>
      uses: azure/setup-helm@v4.2.0
      with:
        version: v3.17.0
      
    - name: Set up chart-testing
      uses: helm/chart-testing-action@v2.7.0

    # even though we specify chart-dirs in ct.yaml, it isn't used by ct for the list-changed command...
    - name: Run chart-testing (list-changed)
      id: list-changed
      run: |
        echo "default_branch: ${{ github.event.repository.default_branch }}"
        changed=$(ct list-changed --remote origin --target-branch ${{ github.event.repository.default_branch }} --chart-dirs deployment/helm/charts)
        echo "list-changed output: $changed"
        if [[ -n "$changed" ]]; then
          echo "changed=true" >> "$GITHUB_OUTPUT"
        fi

    # lint all charts if any changes were detected
    - name: Run chart-testing (lint)
      if: steps.list-changed.outputs.changed == 'true'
      run: ct lint --config ct.yaml --all
      # the following would lint only changed charts, but linting isn't expensive
      # run: ct lint --config ct.yaml --target-branch ${{ github.event.repository.default_branch }}

    - name: Create kind cluster
      if: steps.list-changed.outputs.changed == 'true'
      uses: helm/kind-action@v1.12.0

    - name: Run chart-testing (install)
      if: steps.list-changed.outputs.changed == 'true'
      run: ct install --all --helm-extra-set-args="--set=nginx.enabled=false" --debug --config ct.yaml
      # the following would install only changed charts, but we only have one chart so 
      # don't worry about that for now
      # run: ct install --target-branch ${{ github.event.repository.default_branch }}
