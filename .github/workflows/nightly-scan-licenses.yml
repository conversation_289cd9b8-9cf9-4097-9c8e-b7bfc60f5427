# Scan for problematic software licenses

# trivy has their own rate limiting issues causing this action to flake
# we worked around it by hardcoding to different db repos in env
# can re-enable when they figure it out
# https://github.com/aquasecurity/trivy/discussions/7538
# https://github.com/aquasecurity/trivy-action/issues/389

name: 'Nightly - Scan licenses'
on:
#   schedule:
#     - cron: '0 14 * * *'  # Runs every day at 6 AM PST / 7 AM PDT / 2 PM UTC
  workflow_dispatch:  # Allows manual triggering

permissions:
  actions: read
  contents: read
  security-events: write
  
jobs:
  scan-licenses:
    # See https://runs-on.com/runners/linux/
    runs-on: [runs-on,runner=2cpu-linux-x64,"run-id=${{ github.run_id }}"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
          cache: 'pip'
          cache-dependency-path: |
            backend/requirements/default.txt
            backend/requirements/dev.txt
            backend/requirements/model_server.txt
      
      - name: Get explicit and transitive dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --retries 5 --timeout 30 -r backend/requirements/default.txt
          pip install --retries 5 --timeout 30 -r backend/requirements/dev.txt
          pip install --retries 5 --timeout 30 -r backend/requirements/model_server.txt
          pip freeze > requirements-all.txt
                    
      - name: Check python
        id: license_check_report
        uses: pilosus/action-pip-license-checker@v2
        with:
          requirements: 'requirements-all.txt'
          fail: 'Copyleft'
          exclude: '(?i)^(pylint|aio[-_]*).*'
          
      - name: Print report
        if: ${{ always() }}
        run: echo "${{ steps.license_check_report.outputs.report }}"
      
      - name: Install npm dependencies
        working-directory: ./web
        run: npm ci
        
      - name: Run Trivy vulnerability scanner in repo mode
        uses: aquasecurity/trivy-action@0.28.0
        with:
          scan-type: fs
          scanners: license
          format: table
#           format: sarif
#           output: trivy-results.sarif
          severity: HIGH,CRITICAL

#       - name: Upload Trivy scan results to GitHub Security tab
#         uses: github/codeql-action/upload-sarif@v3
#         with:
#           sarif_file: trivy-results.sarif
