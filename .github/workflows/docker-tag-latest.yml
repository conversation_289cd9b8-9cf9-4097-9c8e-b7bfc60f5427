# This workflow is set up to be manually triggered via the GitHub Action tab.
# Given a version, it will tag those backend and webserver images as "latest".

name: Tag Latest Version

on:
  workflow_dispatch:
    inputs:
      version:
        description: "The version (ie v0.0.1) to tag as latest"
        required: true

jobs:
  tag:
    # See https://runs-on.com/runners/linux/
    # use a lower powered instance since this just does i/o to docker hub
    runs-on: [runs-on, runner=2cpu-linux-x64, "run-id=${{ github.run_id }}"]
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Enable Docker CLI experimental features
        run: echo "DOCKER_CLI_EXPERIMENTAL=enabled" >> $GITHUB_ENV

      - name: Pull, Tag and Push Web Server Image
        run: |
          docker buildx imagetools create -t onyxdotapp/onyx-web-server:latest onyxdotapp/onyx-web-server:${{ github.event.inputs.version }}

      - name: Pull, Tag and Push API Server Image
        run: |
          docker buildx imagetools create -t onyxdotapp/onyx-backend:latest onyxdotapp/onyx-backend:${{ github.event.inputs.version }}
