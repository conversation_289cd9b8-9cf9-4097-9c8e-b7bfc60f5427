name: <PERSON>uild and Push Backend Image on Tag

on:
  push:
    tags:
      - "*"

env:
  REGISTRY_IMAGE: ${{ contains(github.ref_name, 'cloud') && 'onyxdotapp/onyx-backend-cloud' || 'onyxdotapp/onyx-backend' }}
  LATEST_TAG: ${{ contains(github.ref_name, 'latest') }}

jobs:
  build-and-push:
    # TODO: investigate a matrix build like the web container
    # See https://runs-on.com/runners/linux/
    runs-on: [runs-on, runner=8cpu-linux-x64, "run-id=${{ github.run_id }}"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Install build-essential
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential

      - name: Backend Image Docker Build and Push
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}
            ${{ env.LATEST_TAG == 'true' && format('{0}:latest', env.REGISTRY_IMAGE) || '' }}
          build-args: |
            ONYX_VERSION=${{ github.ref_name }}

      # trivy has their own rate limiting issues causing this action to flake
      # we worked around it by hardcoding to different db repos in env
      # can re-enable when they figure it out
      # https://github.com/aquasecurity/trivy/discussions/7538
      # https://github.com/aquasecurity/trivy-action/issues/389
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        env:
          TRIVY_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-db:2"
          TRIVY_JAVA_DB_REPOSITORY: "public.ecr.aws/aquasecurity/trivy-java-db:1"
        with:
          # To run locally: trivy image --severity HIGH,CRITICAL onyxdotapp/onyx-backend
          image-ref: docker.io/${{ env.REGISTRY_IMAGE }}:${{ github.ref_name }}
          severity: "CRITICAL,HIGH"
          trivyignores: ./backend/.trivyignore
