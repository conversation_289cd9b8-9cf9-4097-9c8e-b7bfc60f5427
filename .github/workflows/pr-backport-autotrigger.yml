name: Backport on Merge

# Note this workflow does not trigger the builds, be sure to manually tag the branches to trigger the builds

on:
  pull_request:
    types: [closed] # Later we check for merge so only PRs that go in can get backported

permissions:
  contents: write
  actions: write

jobs:
  backport:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.YUHONG_GH_ACTIONS }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ssh-key: "${{ secrets.RKUO_DEPLOY_KEY }}"
          fetch-depth: 0

      - name: Set up Git user
        run: |
          git config user.name "<PERSON> [bot]"
          git config user.email "rkuo[bot]@onyx.app"
          git fetch --prune

      - name: Check for Backport Checkbox
        id: checkbox-check
        run: |
          PR_BODY="${{ github.event.pull_request.body }}"
          if [[ "$PR_BODY" == *"[x] This PR should be backported"* ]]; then
            echo "backport=true" >> $GITHUB_OUTPUT
          else
            echo "backport=false" >> $GITHUB_OUTPUT
          fi

      - name: List and sort release branches
        id: list-branches
        run: |
          git fetch --all --tags
          BRANCHES=$(git for-each-ref --format='%(refname:short)' refs/remotes/origin/release/* | sed 's|origin/release/||' | sort -Vr)
          BETA=$(echo "$BRANCHES" | head -n 1)
          STABLE=$(echo "$BRANCHES" | head -n 2 | tail -n 1)
          echo "beta=release/$BETA" >> $GITHUB_OUTPUT
          echo "stable=release/$STABLE" >> $GITHUB_OUTPUT
          # Fetch latest tags for beta and stable
          LATEST_BETA_TAG=$(git tag -l "v[0-9]*.[0-9]*.[0-9]*-beta.[0-9]*" | grep -E "^v[0-9]+\.[0-9]+\.[0-9]+-beta\.[0-9]+$" | grep -v -- "-cloud" | sort -Vr | head -n 1)
          LATEST_STABLE_TAG=$(git tag -l "v[0-9]*.[0-9]*.[0-9]*" | grep -E "^v[0-9]+\.[0-9]+\.[0-9]+$" | sort -Vr | head -n 1)

          # Handle case where no beta tags exist
          if [[ -z "$LATEST_BETA_TAG" ]]; then
            NEW_BETA_TAG="v1.0.0-beta.1"
          else
            NEW_BETA_TAG=$(echo $LATEST_BETA_TAG | awk -F '[.-]' '{print $1 "." $2 "." $3 "-beta." ($NF+1)}')
          fi

          # Increment latest stable tag
          NEW_STABLE_TAG=$(echo $LATEST_STABLE_TAG | awk -F '.' '{print $1 "." $2 "." ($3+1)}')
          echo "latest_beta_tag=$LATEST_BETA_TAG" >> $GITHUB_OUTPUT
          echo "latest_stable_tag=$LATEST_STABLE_TAG" >> $GITHUB_OUTPUT
          echo "new_beta_tag=$NEW_BETA_TAG" >> $GITHUB_OUTPUT
          echo "new_stable_tag=$NEW_STABLE_TAG" >> $GITHUB_OUTPUT

      - name: Echo branch and tag information
        run: |
          echo "Beta branch: ${{ steps.list-branches.outputs.beta }}"
          echo "Stable branch: ${{ steps.list-branches.outputs.stable }}"
          echo "Latest beta tag: ${{ steps.list-branches.outputs.latest_beta_tag }}"
          echo "Latest stable tag: ${{ steps.list-branches.outputs.latest_stable_tag }}"
          echo "New beta tag: ${{ steps.list-branches.outputs.new_beta_tag }}"
          echo "New stable tag: ${{ steps.list-branches.outputs.new_stable_tag }}"

      - name: Trigger Backport
        if: steps.checkbox-check.outputs.backport == 'true'
        run: |
          set -e
          echo "Backporting to beta ${{ steps.list-branches.outputs.beta }} and stable ${{ steps.list-branches.outputs.stable }}"

          # Echo the merge commit SHA
          echo "Merge commit SHA: ${{ github.event.pull_request.merge_commit_sha }}"

          # Fetch all history for all branches and tags
          git fetch --prune

          # Reset and prepare the beta branch
          git checkout ${{ steps.list-branches.outputs.beta }}
          echo "Last 5 commits on beta branch:"
          git log -n 5 --pretty=format:"%H"
          echo ""  # Newline for formatting

          # Cherry-pick the merge commit from the merged PR
          git cherry-pick -m 1 ${{ github.event.pull_request.merge_commit_sha }} || {
            echo "Cherry-pick to beta failed due to conflicts."
            exit 1
          }

          # Create new beta branch/tag
          git tag ${{ steps.list-branches.outputs.new_beta_tag }}
          # Push the changes and tag to the beta branch using PAT
          git push origin ${{ steps.list-branches.outputs.beta }}
          git push origin ${{ steps.list-branches.outputs.new_beta_tag }}

          # Reset and prepare the stable branch
          git checkout ${{ steps.list-branches.outputs.stable }}
          echo "Last 5 commits on stable branch:"
          git log -n 5 --pretty=format:"%H"
          echo ""  # Newline for formatting

          # Cherry-pick the merge commit from the merged PR
          git cherry-pick -m 1 ${{ github.event.pull_request.merge_commit_sha }} || {
            echo "Cherry-pick to stable failed due to conflicts."
            exit 1
          }

          # Create new stable branch/tag
          git tag ${{ steps.list-branches.outputs.new_stable_tag }}
          # Push the changes and tag to the stable branch using PAT
          git push origin ${{ steps.list-branches.outputs.stable }}
          git push origin ${{ steps.list-branches.outputs.new_stable_tag }}
