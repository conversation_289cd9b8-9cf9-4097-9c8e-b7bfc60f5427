from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from onyx.auth.schemas import UserRole
from onyx.db.models import User
from onyx.server.auth_check import get_current_user
from onyx.server.features.user_group.models import (
    CreateUserGroupRequest,
    UpdateUserGroupRequest,
    UserGroupResponse,
)
from onyx.db.user_group import (
    create_user_group,
    delete_user_group,
    get_user_groups,
    update_user_group,
)
from onyx.server.utils import get_db

router = APIRouter(prefix="/api/v1/user-groups", tags=["user-groups"])


@router.get("", response_model=UserGroupResponse)
def list_user_groups(
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_db),
):
    """List all user groups."""
    if current_user.role not in [UserRole.ADMIN, UserRole.CURATOR, UserRole.GLOBAL_CURATOR]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    groups = get_user_groups(db_session)
    return UserGroupResponse(groups=groups)


@router.post("")
def create_group(
    request: CreateUserGroupRequest,
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_db),
):
    """Create a new user group."""
    if current_user.role not in [UserRole.ADMIN, UserRole.GLOBAL_CURATOR]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    create_user_group(
        name=request.name,
        user_ids=request.user_ids,
        curator_ids=request.curator_ids,
        db_session=db_session,
    )
    return {"message": "User group created successfully"}


@router.put("/{group_id}")
def update_group(
    group_id: int,
    request: UpdateUserGroupRequest,
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_db),
):
    """Update an existing user group."""
    if current_user.role not in [UserRole.ADMIN, UserRole.GLOBAL_CURATOR]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    update_user_group(
        group_id=group_id,
        name=request.name,
        user_ids=request.user_ids,
        curator_ids=request.curator_ids,
        db_session=db_session,
    )
    return {"message": "User group updated successfully"}


@router.delete("/{group_id}")
def delete_group(
    group_id: int,
    current_user: User = Depends(get_current_user),
    db_session: Session = Depends(get_db),
):
    """Delete a user group."""
    if current_user.role not in [UserRole.ADMIN, UserRole.GLOBAL_CURATOR]:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    delete_user_group(group_id=group_id, db_session=db_session)
    return {"message": "User group deleted successfully"} 