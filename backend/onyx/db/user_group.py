from typing import List
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload

from onyx.db.models import User, UserGroup, User__UserGroup


def get_user_groups(db_session: Session) -> List[UserGroup]:
    """Get all user groups."""
    stmt = (
        select(UserGroup)
        .options(joinedload(UserGroup.users))
        .where(UserGroup.is_up_for_deletion == False)  # noqa: E712
    )
    return list(db_session.execute(stmt).scalars().unique())


def create_user_group(
    name: str,
    user_ids: List[UUID],
    curator_ids: List[UUID],
    db_session: Session,
) -> UserGroup:
    """Create a new user group."""
    group = UserGroup(
        name=name,
        is_up_to_date=False,
        is_up_for_deletion=False,
    )
    db_session.add(group)
    db_session.flush()

    # Add users to the group
    for user_id in user_ids:
        user_group = User__UserGroup(
            user_id=user_id,
            user_group_id=group.id,
            is_curator=user_id in curator_ids,
        )
        db_session.add(user_group)

    db_session.commit()
    return group


def update_user_group(
    group_id: int,
    name: str,
    user_ids: List[UUID],
    curator_ids: List[UUID],
    db_session: Session,
) -> UserGroup:
    """Update an existing user group."""
    group = db_session.get(UserGroup, group_id)
    if not group:
        raise ValueError(f"User group with ID {group_id} not found")

    group.name = name
    group.is_up_to_date = False

    # Remove existing user-group relationships
    stmt = select(User__UserGroup).where(User__UserGroup.user_group_id == group_id)
    existing_relationships = db_session.execute(stmt).scalars().all()
    for relationship in existing_relationships:
        db_session.delete(relationship)

    # Add new user-group relationships
    for user_id in user_ids:
        user_group = User__UserGroup(
            user_id=user_id,
            user_group_id=group.id,
            is_curator=user_id in curator_ids,
        )
        db_session.add(user_group)

    db_session.commit()
    return group


def delete_user_group(group_id: int, db_session: Session) -> None:
    """Delete a user group."""
    group = db_session.get(UserGroup, group_id)
    if not group:
        raise ValueError(f"User group with ID {group_id} not found")

    group.is_up_for_deletion = True
    db_session.commit() 